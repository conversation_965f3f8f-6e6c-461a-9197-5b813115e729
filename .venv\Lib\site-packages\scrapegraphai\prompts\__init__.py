"""
__init__.py for the prompts folder
"""

from .generate_answer_node_csv_prompts import (
    TEMPLATE_CHUKS_CSV,
    TEMPLATE_MERGE_CSV,
    TEMPLATE_NO_CHUKS_CSV,
)
from .generate_answer_node_omni_prompts import (
    TEMPLATE_CHUNKS_OMNI,
    TEMPLATE_MERGE_OMNI,
    TEMPLATE_NO_CHUNKS_OMNI,
)
from .generate_answer_node_pdf_prompts import (
    TEMPLATE_CHUNKS_PDF,
    TEMPLATE_MERGE_PDF,
    TEMPLATE_NO_CHUNKS_PDF,
)
from .generate_answer_node_prompts import (
    REGEN_ADDITIONAL_INFO,
    TEMPLATE_CHUNKS,
    TEMPLATE_CHUNKS_MD,
    TEMPLATE_MERGE,
    TEMPLATE_MERGE_MD,
    TEMPLATE_NO_CHUNKS,
    TEMPLATE_NO_CHUNKS_MD,
)
from .generate_code_node_prompts import (
    TEMPLATE_EXECUTION_ANALYSIS,
    TEMPLATE_EXECUTION_CODE_GENERATION,
    TEMPLATE_INIT_CODE_GENERATION,
    TEMPLATE_SEMANTIC_ANALYSIS,
    TEMPLATE_SEMANTIC_CODE_GENERATION,
    TEMPLATE_SEMANTIC_COMPARISON,
    TEMPLATE_SYNTAX_ANALYSIS,
    TEMPLATE_SYNTAX_CODE_GENERATION,
    TEMPLATE_VALIDATION_ANALYSIS,
    TEMPLATE_VALIDATION_CODE_GENERATION,
)
from .get_probable_tags_node_prompts import TEMPLATE_GET_PROBABLE_TAGS
from .html_analyzer_node_prompts import (
    TEMPLATE_HTML_ANALYSIS,
    TEMPLATE_HTML_ANALYSIS_WITH_CONTEXT,
)
from .merge_answer_node_prompts import TEMPLATE_COMBINED
from .merge_generated_scripts_prompts import TEMPLATE_MERGE_SCRIPTS_PROMPT
from .prompt_refiner_node_prompts import TEMPLATE_REFINER, TEMPLATE_REFINER_WITH_CONTEXT
from .reasoning_node_prompts import TEMPLATE_REASONING, TEMPLATE_REASONING_WITH_CONTEXT
from .robots_node_prompts import TEMPLATE_ROBOT
from .search_internet_node_prompts import TEMPLATE_SEARCH_INTERNET
from .search_link_node_prompts import TEMPLATE_RELEVANT_LINKS
from .search_node_with_context_prompts import (
    TEMPLATE_SEARCH_WITH_CONTEXT_CHUNKS,
    TEMPLATE_SEARCH_WITH_CONTEXT_NO_CHUNKS,
)

__all__ = [
    # CSV Answer Generation Templates
    "TEMPLATE_CHUKS_CSV",
    "TEMPLATE_MERGE_CSV",
    "TEMPLATE_NO_CHUKS_CSV",
    # Omni Answer Generation Templates
    "TEMPLATE_CHUNKS_OMNI",
    "TEMPLATE_MERGE_OMNI",
    "TEMPLATE_NO_CHUNKS_OMNI",
    # PDF Answer Generation Templates
    "TEMPLATE_CHUNKS_PDF",
    "TEMPLATE_MERGE_PDF",
    "TEMPLATE_NO_CHUNKS_PDF",
    # General Answer Generation Templates
    "REGEN_ADDITIONAL_INFO",
    "TEMPLATE_CHUNKS",
    "TEMPLATE_CHUNKS_MD",
    "TEMPLATE_MERGE",
    "TEMPLATE_MERGE_MD",
    "TEMPLATE_NO_CHUNKS",
    "TEMPLATE_NO_CHUNKS_MD",
    # Code Generation and Analysis Templates
    "TEMPLATE_EXECUTION_ANALYSIS",
    "TEMPLATE_EXECUTION_CODE_GENERATION",
    "TEMPLATE_INIT_CODE_GENERATION",
    "TEMPLATE_SEMANTIC_ANALYSIS",
    "TEMPLATE_SEMANTIC_CODE_GENERATION",
    "TEMPLATE_SEMANTIC_COMPARISON",
    "TEMPLATE_SYNTAX_ANALYSIS",
    "TEMPLATE_SYNTAX_CODE_GENERATION",
    "TEMPLATE_VALIDATION_ANALYSIS",
    "TEMPLATE_VALIDATION_CODE_GENERATION",
    # HTML and Tag Analysis Templates
    "TEMPLATE_GET_PROBABLE_TAGS",
    "TEMPLATE_HTML_ANALYSIS",
    "TEMPLATE_HTML_ANALYSIS_WITH_CONTEXT",
    # Merging and Combining Templates
    "TEMPLATE_COMBINED",
    "TEMPLATE_MERGE_SCRIPTS_PROMPT",
    # Search and Context Templates
    "TEMPLATE_SEARCH_INTERNET",
    "TEMPLATE_RELEVANT_LINKS",
    "TEMPLATE_SEARCH_WITH_CONTEXT_CHUNKS",
    "TEMPLATE_SEARCH_WITH_CONTEXT_NO_CHUNKS",
    # Reasoning and Refinement Templates
    "TEMPLATE_REFINER",
    "TEMPLATE_REFINER_WITH_CONTEXT",
    "TEMPLATE_REASONING",
    "TEMPLATE_REASONING_WITH_CONTEXT",
    # Robot Templates
    "TEMPLATE_ROBOT",
]
