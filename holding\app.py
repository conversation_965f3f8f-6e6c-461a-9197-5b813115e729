import os
import uuid
from flask import Flask, render_template, request, jsonify
from flask_socketio import Socket<PERSON>, emit # For future Pi communication
import requests
from bs4 import BeautifulSoup # For web scraping
import xml.etree.ElementTree as ET # For parsing CAP XML
from datetime import datetime, timezone, timedelta
import json
import logging
import re

# --- Flask App Setup ---
app = Flask(__name__) # Use default templates folder
app.config['SECRET_KEY'] = 'emergency_secret_key!' # CHANGE THIS!
socketio = SocketIO(app) # For future Pi client updates

# --- Configuration & Constants ---
# Open-Meteo API
OPEN_METEO_API_URL = "https://api.open-meteo.com/v1/forecast"
ACCRA_LATITUDE = 5.55602
ACCRA_LONGITUDE = -0.1969
DEFAULT_LOCATION_DESC = "Accra, Greater Accra Region, Ghana"

# GHAAP Agro-Climate (Example URL - make this configurable or dynamic later)
# Default: Ashanti region, Ejisu Juaben district, Maize, current year
GHAAP_DEFAULT_REGION = "REG02" # Ashanti Region
GHAAP_DEFAULT_DISTRICT = "DS031" # Ejisu
GHAAP_DEFAULT_CROP = "CT0000000001" # Maize
GHAAP_BASE_URL = "https://ghaap.com/weather-forecast/index.php"

# CAP Message Sender Information (for internally generated messages if any)
CAP_SENDER_ID = "<EMAIL>"
CAP_SENDER_NAME = "Emergency Communications Platform - HQ"

# AI Configuration
AI_SERVICE = os.getenv('AI_SERVICE', 'openai')  # 'openai' or 'ollama'
OLLAMA_BASE_URL = os.getenv('OLLAMA_BASE_URL', 'http://localhost:11434')
OLLAMA_MODEL = os.getenv('OLLAMA_MODEL', 'llama3')
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', '')
OPENAI_MODEL = os.getenv('OPENAI_MODEL', 'gpt-3.5-turbo')

# Logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- Data Storage (Simple In-Memory) ---
# Stores the latest processed data for each source type
latest_data_store = {
    "cap": None,
    "meteo": None,
    "agro": None
}

# --- Helper Functions (Open-Meteo & CAP Parsing - adapted from cap_generator_py) ---
def get_utc_timestamp():
    return datetime.now(timezone.utc).isoformat(timespec='seconds')

def interpret_weather_code(code):
    codes = {
        0: "Clear sky", 1: "Mainly clear", 2: "Partly cloudy", 3: "Overcast",
        45: "Fog", 48: "Depositing rime fog",
        51: "Light drizzle", 53: "Moderate drizzle", 55: "Dense drizzle",
        61: "Slight rain", 63: "Moderate rain", 65: "Heavy rain",
        71: "Slight snow fall", 73: "Moderate snow fall", 75: "Heavy snow fall",
        80: "Slight rain showers", 81: "Moderate rain showers", 82: "Violent rain showers",
        95: "Thunderstorm", 96: "Thunderstorm with hail", 99: "Thunderstorm with heavy hail"
    }
    return codes.get(code, f"Weather code: {code}")

def summarize_text_with_ollama(text, max_length=150):
    """
    Summarize text using Ollama API.
    """
    try:
        prompt = f"""Please summarize the following text in a clear, concise manner suitable for emergency communications.
Keep it under {max_length} characters and focus on the most important information:

{text}

Summary:"""

        payload = {
            "model": OLLAMA_MODEL,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": 0.3,
                "top_p": 0.9,
                "max_tokens": 100
            }
        }

        response = requests.post(
            f"{OLLAMA_BASE_URL}/api/generate",
            json=payload,
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()
            summary = result.get('response', '').strip()
            if summary and len(summary) > 10:  # Basic validation
                return summary[:max_length] if len(summary) > max_length else summary
            else:
                logging.warning("Ollama returned empty or very short summary")
                return text[:max_length] + "..." if len(text) > max_length else text
        else:
            logging.error(f"Ollama API error: {response.status_code}")
            return text[:max_length] + "..." if len(text) > max_length else text

    except Exception as e:
        logging.error(f"Error with Ollama summarization: {e}")
        return text[:max_length] + "..." if len(text) > max_length else text

def summarize_text_with_openai(text, max_length=150):
    """
    Summarize text using OpenAI API.
    """
    try:
        if not OPENAI_API_KEY:
            logging.warning("OpenAI API key not provided")
            return text[:max_length] + "..." if len(text) > max_length else text

        headers = {
            "Authorization": f"Bearer {OPENAI_API_KEY}",
            "Content-Type": "application/json"
        }

        payload = {
            "model": OPENAI_MODEL,
            "messages": [
                {
                    "role": "system",
                    "content": "You are an expert at summarizing emergency and weather information. Provide clear, concise summaries suitable for emergency communications."
                },
                {
                    "role": "user",
                    "content": f"Please summarize the following text in under {max_length} characters, focusing on the most important information:\n\n{text}"
                }
            ],
            "max_tokens": 100,
            "temperature": 0.3
        }

        response = requests.post(
            "https://api.openai.com/v1/chat/completions",
            headers=headers,
            json=payload,
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()
            summary = result['choices'][0]['message']['content'].strip()
            if summary and len(summary) > 10:  # Basic validation
                return summary[:max_length] if len(summary) > max_length else summary
            else:
                logging.warning("OpenAI returned empty or very short summary")
                return text[:max_length] + "..." if len(text) > max_length else text
        else:
            logging.error(f"OpenAI API error: {response.status_code}")
            return text[:max_length] + "..." if len(text) > max_length else text

    except Exception as e:
        logging.error(f"Error with OpenAI summarization: {e}")
        return text[:max_length] + "..." if len(text) > max_length else text

def summarize_advisory_text(text, max_length=150):
    """
    Summarize advisory text using AI services with intelligent fallback.
    Priority: OpenAI -> Ollama -> Simple truncation
    """
    if not text or len(text) <= max_length:
        return text

    logging.info(f"Starting AI summarization (original length: {len(text)} chars)")

    # Try OpenAI first (most reliable)
    if OPENAI_API_KEY:
        logging.info("Attempting OpenAI summarization...")
        result = summarize_text_with_openai(text, max_length)
        # Check if OpenAI actually worked (not just truncated)
        if result != text[:max_length] + "..." and len(result) < len(text):
            logging.info(f"OpenAI summarization successful (new length: {len(result)} chars)")
            return result
        else:
            logging.warning("OpenAI summarization failed, trying Ollama...")
    else:
        logging.info("No OpenAI API key provided, trying Ollama...")

    # Try Ollama as backup
    try:
        # Quick check if Ollama is available
        import requests
        response = requests.get(f"{OLLAMA_BASE_URL}/api/version", timeout=2)
        if response.status_code == 200:
            logging.info("Ollama is available, attempting summarization...")
            result = summarize_text_with_ollama(text, max_length)
            # Check if Ollama actually worked (not just truncated)
            if result != text[:max_length] + "..." and len(result) < len(text):
                logging.info(f"Ollama summarization successful (new length: {len(result)} chars)")
                return result
            else:
                logging.warning("Ollama summarization failed, using intelligent truncation...")
        else:
            logging.warning("Ollama not available, using intelligent truncation...")
    except Exception as e:
        logging.warning(f"Ollama connection failed: {e}, using intelligent truncation...")

    # Intelligent truncation as final fallback
    logging.info("Using intelligent truncation as fallback")
    return intelligent_truncate(text, max_length)

def intelligent_truncate(text, max_length=150):
    """
    Intelligently truncate text by preserving important information.
    """
    if len(text) <= max_length:
        return text

    # Try to break at sentence boundaries
    sentences = text.split('. ')
    if len(sentences) > 1:
        result = ""
        for sentence in sentences:
            if len(result + sentence + ". ") <= max_length - 3:  # Leave room for "..."
                result += sentence + ". "
            else:
                break
        if result:
            return result.rstrip() + "..."

    # If no good sentence break, truncate at word boundary
    words = text.split()
    result = ""
    for word in words:
        if len(result + word + " ") <= max_length - 3:
            result += word + " "
        else:
            break

    return result.rstrip() + "..." if result else text[:max_length-3] + "..."

def fetch_open_meteo_data(lat=ACCRA_LATITUDE, lon=ACCRA_LONGITUDE, location_desc=DEFAULT_LOCATION_DESC):
    params = {
        "latitude": lat,
        "longitude": lon,
        "current_weather": "true",
        "hourly": "temperature_2m,relativehumidity_2m,apparent_temperature,precipitation_probability,weathercode,windspeed_10m,winddirection_10m",
        "daily": "weathercode,temperature_2m_max,temperature_2m_min,precipitation_sum,precipitation_probability_max",
        "timezone": "auto"
    }
    try:
        response = requests.get(OPEN_METEO_API_URL, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()

        current_weather = data.get("current_weather", {})
        daily_weather = data.get("daily", {})

        # Construct a user-friendly advisory
        temp = current_weather.get('temperature', 'N/A')
        windspeed = current_weather.get('windspeed', 'N/A')
        weather_code = current_weather.get('weathercode')
        condition = interpret_weather_code(weather_code)

        humidity = "N/A"
        if "hourly" in data and "relativehumidity_2m" in data["hourly"] and data["hourly"]["relativehumidity_2m"]:
            humidity = data["hourly"]["relativehumidity_2m"][0] # Example: first hour's humidity

        precip_prob = "N/A"
        if "hourly" in data and "precipitation_probability" in data["hourly"] and data["hourly"]["precipitation_probability"]:
             precip_prob = data["hourly"]["precipitation_probability"][0]


        advisory_text = (
            f"Weather for {location_desc}: {condition}. "
            f"Temp: {temp}°C. Humidity: {humidity}%. "
            f"Wind: {windspeed} km/h. Precipitation Chance: {precip_prob}%."
        )

        # Summarize the advisory text using AI
        summarized_advisory = summarize_advisory_text(advisory_text, max_length=200)

        # Data for UI and Pi
        processed_data = {
            "source": "meteo",
            "location": location_desc,
            "condition": condition,
            "temperature": temp,
            "humidity": humidity,
            "windspeed": windspeed,
            "precipitation_probability": precip_prob,
            "weather_code": weather_code, # For potential icon mapping in UI
            "advisory_text": summarized_advisory, # For UI display (AI summarized)
            "original_advisory_text": advisory_text, # Keep original for reference
            "audio_text": f"Weather update for {location_desc}. Condition: {condition}. Temperature is {temp} degrees Celsius.", # For TTS
            "timestamp": get_utc_timestamp(),
            "icon_emoji": "🌤️" # Default, can be changed based on weather_code
        }

        # Basic icon logic (can be expanded)
        if weather_code is not None:
            if weather_code in [0, 1]: processed_data["icon_emoji"] = "☀️" # Sunny
            elif weather_code in [2, 3]: processed_data["icon_emoji"] = "☁️" # Cloudy
            elif weather_code >= 51 and weather_code <= 67: processed_data["icon_emoji"] = "🌧️" # Rainy
            elif weather_code >= 95 : processed_data["icon_emoji"] = "⛈️" # Thunderstorm

        latest_data_store["meteo"] = processed_data
        return processed_data

    except requests.exceptions.RequestException as e:
        logging.error(f"Error fetching Open-Meteo data: {e}")
        return {"error": str(e), "advisory_text": "Could not fetch weather data."}
    except Exception as e:
        logging.error(f"Error processing Open-Meteo data: {e}")
        return {"error": str(e), "advisory_text": "Error processing weather data."}


def parse_cap_alert_xml(cap_xml_string):
    try:
        root = ET.fromstring(cap_xml_string)
        ns = {'cap': 'urn:oasis:names:tc:emergency:cap:1.2'}

        # Extract key elements
        identifier = root.find('cap:identifier', ns).text if root.find('cap:identifier', ns) is not None else str(uuid.uuid4())
        sender = root.find('cap:sender', ns).text if root.find('cap:sender', ns) is not None else CAP_SENDER_ID
        sent_time_cap = root.find('cap:sent', ns).text if root.find('cap:sent', ns) is not None else get_utc_timestamp()

        # CAP allows multiple <info> blocks, process the first one for simplicity
        info_block = root.find('cap:info', ns)
        if not info_block:
            return {"error": "No <info> block found in CAP XML.", "advisory_text": "Invalid CAP format."}

        headline = info_block.find('cap:headline', ns).text if info_block.find('cap:headline', ns) is not None else "N/A"
        description = info_block.find('cap:description', ns).text if info_block.find('cap:description', ns) is not None else "N/A"
        area_desc_element = info_block.find('cap:area/cap:areaDesc', ns)
        area_description = area_desc_element.text if area_desc_element is not None else "Not specified"
        event = info_block.find('cap:event', ns).text if info_block.find('cap:event', ns) is not None else "Alert"

        advisory_text = f"CAP Alert: {headline}. Description: {description}. Area: {area_description}."
        audio_text = f"Emergency Alert. {event}. {headline}. {description}. Area affected: {area_description}."

        # Summarize the advisory text using AI
        summarized_advisory = summarize_advisory_text(advisory_text, max_length=200)

        processed_data = {
            "source": "cap",
            "identifier": identifier,
            "sender": sender,
            "sent_time_cap": sent_time_cap, # Time from CAP message
            "headline": headline,
            "description": description,
            "area_description": area_description,
            "event": event,
            "advisory_text": summarized_advisory, # For UI display (AI summarized)
            "original_advisory_text": advisory_text, # Keep original for reference
            "audio_text": audio_text,       # For TTS
            "timestamp": get_utc_timestamp(), # Time processed by this system
            "icon_emoji": "⚡"
        }
        latest_data_store["cap"] = processed_data
        return processed_data

    except ET.ParseError as e:
        logging.error(f"Error parsing CAP XML: {e}")
        return {"error": f"Invalid CAP XML format: {e}", "advisory_text": "Invalid CAP XML provided."}
    except Exception as e:
        logging.error(f"Unexpected error processing CAP XML: {e}")
        return {"error": str(e), "advisory_text": "Error processing CAP alert."}


def fetch_ghaap_agro_data(region=GHAAP_DEFAULT_REGION, district=GHAAP_DEFAULT_DISTRICT, crop=GHAAP_DEFAULT_CROP, year="2025"):
    """
    Fetches agricultural advisory data from GHAAP website using AI-powered scraping with OpenAI.
    Now properly uses the provided parameters for targeted scraping.
    """
    try:
        # Import the enhanced scraper function
        from scrape_weather import get_ghaap_advisory

        logging.info(f"Fetching GHAAP agricultural advisory data for Region: {region}, District: {district}, Crop: {crop}, Year: {year}")

        # Use AI-powered scraping with the specific parameters
        result = get_ghaap_advisory(
            region=region,
            district=district,
            crop=crop,
            year=year,
            use_ai=True,
            force_model=None
        )

        if result and result.get('method') != 'failed':
            # Get the raw advisory text for summarization
            raw_advisory_text = result.get('raw_advisory_text', result.get('advisory_text', 'No advisory text available'))
            formatted_advisory_text = result.get('advisory_text', 'No advisory text available')

            # Apply additional AI summarization to make it even more concise for UI
            summarized_advisory = summarize_advisory_text(raw_advisory_text, max_length=200)

            # Extract regional information from AI result if available
            ai_data = result.get('data', {})
            region_info = ai_data.get('regional_information', 'Central Region') if isinstance(ai_data, dict) else "Central Region"
            if region_info == "Not available":
                region_info = "Central Region"

            # Extract crop information from AI result if available
            crop_info = ai_data.get('crop_information', 'Maize') if isinstance(ai_data, dict) else "Maize"
            if crop_info == "Not available":
                crop_info = "Maize"

            processed_data = {
                "source": "agro",
                "method": result.get('method', 'unknown'),
                "ai_model": result.get('ai_model', 'unknown'),
                "region": region_info,
                "district": "Assin Central District",  # Default for now
                "crop": crop_info,
                "year": datetime.now().year,
                "advisory_text": summarized_advisory,  # For UI display (double AI processed)
                "original_advisory_text": raw_advisory_text,  # Keep original for reference
                "formatted_advisory_text": formatted_advisory_text,  # Intermediate formatting
                "ai_extracted_data": ai_data,  # Full AI extraction result
                "audio_text": f"Agro-climate advisory from GHAAP. {summarized_advisory[:200]}..." if len(summarized_advisory) > 200 else f"Agro-climate advisory from GHAAP. {summarized_advisory}",
                "timestamp": get_utc_timestamp(),
                "icon_emoji": "🌾",
                "data_source": result.get('source', 'GHAAP'),
                "scraping_timestamp": result.get('timestamp', 'unknown')
            }

            latest_data_store["agro"] = processed_data
            logging.info(f"Successfully fetched GHAAP data using {result.get('method')} method with AI model: {result.get('ai_model', 'unknown')}")
            return processed_data
        else:
            error_msg = result.get('error', 'Unknown error occurred') if result else 'No data returned from scraper'
            logging.error(f"GHAAP scraping failed: {error_msg}")
            return {
                "error": error_msg,
                "advisory_text": result.get('advisory_text', 'Could not fetch GHAAP agro-advisory. Please check the website directly.'),
                "source": "agro",
                "timestamp": get_utc_timestamp()
            }

    except ImportError as e:
        logging.error(f"Could not import scrape_weather module: {e}")
        return {"error": "Scraper module not available", "advisory_text": "Could not load GHAAP scraper."}
    except Exception as e:
        logging.error(f"Error processing GHAAP data: {e}")
        return {"error": str(e), "advisory_text": "Error processing GHAAP agro-advisory."}


# --- Flask Routes ---
@app.route('/')
def index():
    """Serves the main HTML page."""
    return render_template('Home.html') # Serves Home.html from templates folder

@app.route('/ai_config', methods=['GET'])
def get_ai_config():
    """Returns current AI configuration and availability for both scraping and summarization."""
    # Check Ollama availability
    ollama_available = False
    try:
        response = requests.get(f"{OLLAMA_BASE_URL}/api/version", timeout=2)
        ollama_available = response.status_code == 200
    except:
        ollama_available = False

    return jsonify({
        "text_summarization": {
            "primary_service": "OpenAI" if OPENAI_API_KEY else "Ollama",
            "fallback_service": "Ollama" if OPENAI_API_KEY else "Intelligent Truncation",
            "strategy": "OpenAI -> Ollama -> Intelligent Truncation"
        },
        "web_scraping": {
            "primary_service": "ScrapeGraphAI with OpenAI" if OPENAI_API_KEY else "ScrapeGraphAI with Ollama",
            "fallback_service": "Traditional scraping",
            "strategy": "AI Scraping (OpenAI/Ollama) -> Traditional Scraping"
        },
        "services_available": {
            "openai_available": bool(OPENAI_API_KEY),
            "ollama_available": ollama_available,
            "scrapegraphai_available": True  # Assuming it's installed
        },
        "models": {
            "openai_model": OPENAI_MODEL,
            "ollama_model": OLLAMA_MODEL
        },
        "overall_strategy": "Dual AI: ScrapeGraphAI for extraction + OpenAI/Ollama for summarization"
    })

@app.route('/test_ai_summary', methods=['POST'])
def test_ai_summary():
    """Test endpoint for AI summarization."""
    try:
        data = request.get_json()
        text = data.get('text', '')
        max_length = data.get('max_length', 150)

        if not text:
            return jsonify({"error": "No text provided"}), 400

        summary = summarize_advisory_text(text, max_length)

        return jsonify({
            "original_text": text,
            "summary": summary,
            "original_length": len(text),
            "summary_length": len(summary),
            "ai_service": AI_SERVICE,
            "compression_ratio": round(len(summary) / len(text) * 100, 1) if text else 0
        })

    except Exception as e:
        logging.error(f"Error in test_ai_summary: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/claude')
def claude():
    """Serves the claude.html page if available."""
    try:
        return render_template('claude.html')
    except:
        return "claude.html template not found. Please add it to the templates folder.", 404

@app.route('/fetch_data', methods=['GET'])
def handle_fetch_data():
    source = request.args.get('source', '').lower()
    logging.info(f"Received request to fetch data for source: {source}")
    data = None
    if source == 'meteo':
        # Could add lat/lon from request.args if needed
        data = fetch_open_meteo_data()
    elif source == 'agro':
        # Get parameters from request or use defaults
        region = request.args.get('region', GHAAP_DEFAULT_REGION)
        district = request.args.get('district', GHAAP_DEFAULT_DISTRICT)
        crop = request.args.get('crop', GHAAP_DEFAULT_CROP)
        year = request.args.get('year', '2025')

        logging.info(f"Fetching agro data with params - Region: {region}, District: {district}, Crop: {crop}, Year: {year}")
        data = fetch_ghaap_agro_data(region=region, district=district, crop=crop, year=year)
    else:
        return jsonify({"error": "Invalid data source specified."}), 400

    if data and "error" not in data:
        # Emit to Pi clients (future enhancement)
        # socketio.emit('new_alert_data', data, namespace='/pi_client')
        return jsonify(data)
    else:
        return jsonify(data if data else {"error": "Failed to fetch data for unknown reason."}), 500

@app.route('/submit_cap', methods=['POST'])
def handle_submit_cap():
    try:
        cap_xml = request.data.decode('utf-8')
        if not cap_xml:
            return jsonify({"error": "No CAP XML data received."}), 400

        logging.info(f"Received CAP XML for processing. Length: {len(cap_xml)}")
        data = parse_cap_alert_xml(cap_xml)

        if data and "error" not in data:
            # Emit to Pi clients (future enhancement)
            # socketio.emit('new_alert_data', data, namespace='/pi_client')
            return jsonify(data)
        else:
            return jsonify(data if data else {"error": "Failed to parse CAP XML."}), 400
    except Exception as e:
        logging.error(f"Error in /submit_cap endpoint: {e}")
        return jsonify({"error": f"Server error processing CAP: {str(e)}"}), 500

# --- SocketIO Events (for future Pi client) ---
@socketio.on('connect', namespace='/pi_client') # Example namespace for Pi clients
def handle_pi_connect():
    logging.info(f"Raspberry Pi client connected: {request.sid}")
    # Optionally send current state or a welcome message
    # emit('connection_ack', {'message': 'Connected to Emergency Platform Server'})

@socketio.on('disconnect', namespace='/pi_client')
def handle_pi_disconnect():
    logging.info(f"Raspberry Pi client disconnected: {request.sid}")


# --- Main Execution ---
if __name__ == '__main__':
    logging.info("Starting Emergency Communications Platform server...")
    # For development: flask run --host=0.0.0.0 --port=5000
    # Or using socketio.run for integrated SocketIO development server:
    socketio.run(app, host='0.0.0.0', port=5000, debug=True, use_reloader=True)
