"""
List of model tokens
"""

models_tokens = {
    "openai": {
        "gpt-3.5-turbo-0125": 16385,
        "gpt-3.5": 4096,
        "gpt-3.5-turbo": 16385,
        "gpt-3.5-turbo-1106": 16385,
        "gpt-3.5-turbo-instruct": 4096,
        "gpt-4-0125-preview": 128000,
        "gpt-4-turbo-preview": 128000,
        "gpt-4-turbo": 128000,
        "gpt-4-turbo-2024-04-09": 128000,
        "gpt-4-1106-preview": 128000,
        "gpt-4o-search-preview": 128000,
        "gpt-4-vision-preview": 128000,
        "gpt-4": 8192,
        "gpt-4-0613": 8192,
        "gpt-4-32k": 32768,
        "gpt-4-32k-0613": 32768,
        "gpt-4o": 128000,
        "gpt-4o-2024-08-06": 128000,
        "gpt-4o-2024-05-13": 128000,
        "gpt-4o-mini": 128000,
        "gpt-4.1": 1000000,
        "gpt-4.1-mini": 1000000,
        "gpt-4.1-nano": 1000000,
        "gpt-4.5": 128000,
        "gpt-4.5-preview": 128000,
        "o1-preview": 128000,
        "o1-mini": 128000,
        "o1": 128000,
        "gpt-4.5-preview": 128000,
        "o3-mini": 200000,
    },
    "azure_openai": {
        "gpt-3.5-turbo-0125": 16385,
        "gpt-3.5": 4096,
        "gpt-3.5-turbo": 16385,
        "gpt-3.5-turbo-1106": 16385,
        "gpt-3.5-turbo-instruct": 4096,
        "gpt-4-0125-preview": 128000,
        "gpt-4-turbo-preview": 128000,
        "gpt-4-turbo": 128000,
        "gpt-4-turbo-2024-04-09": 128000,
        "gpt-4-1106-preview": 128000,
        "gpt-4-vision-preview": 128000,
        "gpt-4": 8192,
        "gpt-4-0613": 8192,
        "gpt-4-32k": 32768,
        "gpt-4-32k-0613": 32768,
        "gpt-4o": 128000,
        "gpt-4o-mini": 128000,
        "chatgpt-4o-latest": 128000,
        "o1-preview": 128000,
        "o1-mini": 128000,
    },
    "google_genai": {
        "gemini-pro": 128000,
        "gemini-1.5-flash-latest": 128000,
        "gemini-2.0-flash-latest": 128000,
        "gemini-1.5-pro-latest": 128000,
        "models/embedding-001": 2048,
    },
    "google_vertexai": {
        "gemini-1.5-flash": 128000,
        "gemini-1.5-pro": 128000,
        "gemini-1.0-pro": 128000,
    },
    "ollama": {
        "command-r": 12800,
        "codellama": 16000,
        "dbrx": 32768,
        "deepseek-coder:33b": 16000,
        "falcon": 2048,
        "llama2": 4096,
        "llama2:7b": 4096,
        "llama2:13b": 4096,
        "llama2:70b": 4096,
        "llama3": 8192,
        "llama3:8b": 8192,
        "llama3:70b": 8192,
        "llama3.1": 128000,
        "llama3.1:8b": 128000,
        "llama3.1:70b": 128000,
        "lama3.1:405b": 128000,
        "llama3.2": 128000,
        "llama3.2:1b": 128000,
        "llama3.2:3b": 128000,
        "llama3.3": 128000,
        "llama3.3:70b": 128000,
        "scrapegraph": 8192,
        "mistral-small": 128000,
        "mistral-openorca": 32000,
        "mistral-large": 128000,
        "grok-1": 8192,
        "llava": 4096,
        "mixtral:8x22b-instruct": 65536,
        "nomic-embed-text": 8192,
        "nous-hermes2:34b": 4096,
        "orca-mini": 2048,
        "phi3:3.8b": 12800,
        "phi3:14b": 128000,
        "qwen:0.5b": 32000,
        "qwen:1.8b": 32000,
        "qwen:4b": 32000,
        "qwen:14b": 32000,
        "qwen:32b": 32000,
        "qwen:72b": 32000,
        "qwen:110b": 32000,
        "stablelm-zephyr": 8192,
        "wizardlm2:8x22b": 65536,
        "mistral": 128000,
        "gemma2": 128000,
        "gemma2:9b": 128000,
        "gemma2:27b": 128000,
        # embedding models
        "shaw/dmeta-embedding-zh-small-q4": 8192,
        "shaw/dmeta-embedding-zh-q4": 8192,
        "chevalblanc/acge_text_embedding": 8192,
        "martcreation/dmeta-embedding-zh": 8192,
        "snowflake-arctic-embed": 8192,
        "mxbai-embed-large": 512,
    },
    "oneapi": {
        "qwen-turbo": 6000,
    },
    "nvidia": {
        "meta/llama3-70b-instruct": 419,
        "meta/llama3-8b-instruct": 419,
        "nemotron-4-340b-instruct": 1024,
        "databricks/dbrx-instruct": 4096,
        "google/codegemma-7b": 8192,
        "google/gemma-2b": 2048,
        "google/gemma-7b": 8192,
        "google/recurrentgemma-2b": 2048,
        "meta/codellama-70b": 16384,
        "meta/llama2-70b": 4096,
        "microsoft/phi-3-mini-128k-instruct": 122880,
        "mistralai/mistral-7b-instruct-v0.2": 4096,
        "mistralai/mistral-large": 8192,
        "mistralai/mixtral-8x22b-instruct-v0.1": 32768,
        "mistralai/mixtral-8x7b-instruct-v0.1": 8192,
        "snowflake/arctic": 16384,
    },
    "groq": {
        "llama3-8b-8192": 8192,
        "llama3-70b-8192": 8192,
        "mixtral-8x7b-32768": 32768,
        "gemma-7b-it": 8192,
        "claude-3-haiku-20240307'": 8192,
    },
    "toghetherai": {
        "meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo": 128000,
        "meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo": 128000,
        "mistralai/Mixtral-8x22B-Instruct-v0.1": 128000,
        "stabilityai/stable-diffusion-xl-base-1.0": 2048,
        "meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo": 128000,
        "NousResearch/Hermes-3-Llama-3.1-405B-Turbo": 128000,
        "Gryphe/MythoMax-L2-13b-Lite": 8192,
        "Salesforce/Llama-Rank-V1": 8192,
        "meta-llama/Meta-Llama-Guard-3-8B": 128000,
        "meta-llama/Meta-Llama-3-70B-Instruct-Turbo": 128000,
        "meta-llama/Llama-3-8b-chat-hf": 8192,
        "meta-llama/Llama-3-70b-chat-hf": 8192,
        "Qwen/Qwen2-72B-Instruct": 128000,
        "google/gemma-2-27b-it": 8192,
    },
    "anthropic": {
        "claude_instant": 100000,
        "claude2": 9000,
        "claude2.1": 200000,
        "claude3": 200000,
        "claude3.5": 200000,
        "claude-3-opus-20240229": 200000,
        "claude-3-sonnet-20240229": 200000,
        "claude-3-haiku-20240307": 200000,
        "claude-3-5-sonnet-20240620": 200000,
        "claude-3-5-haiku-latest": 200000,
        "claude-opus-4-20250514": 200000,
        "claude-sonnet-4-20250514": 200000,
        "claude-3-7-sonnet-20250219": 200000,
    },
    "bedrock": {
        "anthropic.claude-3-haiku-20240307-v1:0": 200000,
        "anthropic.claude-3-sonnet-20240229-v1:0": 200000,
        "anthropic.claude-3-opus-20240229-v1:0": 200000,
        "anthropic.claude-3-5-sonnet-20240620-v1:0": 200000,
        "claude-3-5-haiku-latest": 200000,
        "anthropic.claude-v2:1": 200000,
        "anthropic.claude-v2": 100000,
        "anthropic.claude-instant-v1": 100000,
        "meta.llama3-8b-instruct-v1:0": 8192,
        "meta.llama3-70b-instruct-v1:0": 8192,
        "meta.llama2-13b-chat-v1": 4096,
        "meta.llama2-70b-chat-v1": 4096,
        "mistral.mistral-7b-instruct-v0:2": 32768,
        "mistral.mixtral-8x7b-instruct-v0:1": 32768,
        "mistral.mistral-large-2402-v1:0": 32768,
        "mistral.mistral-small-2402-v1:0": 32768,
        "amazon.titan-embed-text-v1": 8000,
        "amazon.titan-embed-text-v2:0": 8000,
        "cohere.embed-english-v3": 512,
        "cohere.embed-multilingual-v3": 512,
    },
    "mistralai": {
        "mistral-large-latest": 128000,
        "open-mistral-nemo": 128000,
        "codestral-latest": 32000,
        "mistral-embed": 8000,
        "open-mistral-7b": 32000,
        "open-mixtral-8x7b": 32000,
        "open-mixtral-8x22b": 64000,
        "open-codestral-mamba": 256000,
    },
    "hugging_face": {
        "xai-org/grok-1": 8192,
        "meta-llama/Meta-Llama-3-8B": 8192,
        "meta-llama/Meta-Llama-3-8B-Instruct": 8192,
        "meta-llama/Meta-Llama-3-70B": 8192,
        "meta-llama/Meta-Llama-3-70B-Instruct": 8192,
        "google/gemma-2b": 8192,
        "google/gemma-2b-it": 8192,
        "google/gemma-7b": 8192,
        "google/gemma-7b-it": 8192,
        "microsoft/phi-2": 2048,
        "openai-community/gpt2": 1024,
        "openai-community/gpt2-medium": 1024,
        "openai-community/gpt2-large": 1024,
        "facebook/opt-125m": 2048,
        "petals-team/StableBeluga2": 8192,
        "distilbert/distilgpt2": 1024,
        "mistralai/Mistral-7B-Instruct-v0.2": 32768,
        "gradientai/Llama-3-8B-Instruct-Gradient-1048k": 1040200,
        "NousResearch/Hermes-2-Pro-Llama-3-8B": 8192,
        "NousResearch/Hermes-2-Pro-Llama-3-8B-GGUF": 8192,
        "nvidia/Llama3-ChatQA-1.5-8B": 8192,
        "microsoft/Phi-3-mini-4k-instruct": 4192,
        "microsoft/Phi-3-mini-128k-instruct": 131072,
        "mlabonne/Meta-Llama-3-120B-Instruct": 8192,
        "cognitivecomputations/dolphin-2.9-llama3-8b": 8192,
        "cognitivecomputations/dolphin-2.9-llama3-8b-gguf": 8192,
        "cognitivecomputations/dolphin-2.8-mistral-7b-v02": 32768,
        "cognitivecomputations/dolphin-2.5-mixtral-8x7b": 32768,
        "TheBloke/dolphin-2.7-mixtral-8x7b-GGUF": 32768,
        "deepseek-ai/DeepSeek-V2": 131072,
        "deepseek-ai/DeepSeek-V2-Chat": 131072,
        "claude-3-haiku": 200000,
    },
    "deepseek": {
        "deepseek-chat": 28672,
        "deepseek-coder": 16384,
    },
    "ernie": {
        "ernie-bot-turbo": 4096,
        "ernie-bot": 4096,
        "ernie-bot-2": 4096,
        "ernie-bot-2-base": 4096,
        "ernie-bot-2-base-zh": 4096,
        "ernie-bot-2-base-en": 4096,
        "ernie-bot-2-base-en-zh": 4096,
        "ernie-bot-2-base-zh-en": 4096,
    },
    "fireworks": {
        "llama-v2-7b": 4096,
        "mixtral-8x7b-instruct": 4096,
        "nomic-ai/nomic-embed-text-v1.5": 8192,
        "llama-3.1-405B-instruct": 131072,
        "llama-3.1-70B-instruct": 131072,
        "llama-3.1-8B-instruct": 131072,
        "mixtral-moe-8x22B-instruct": 65536,
        "mixtral-moe-8x7B-instruct": 65536,
    },
    "clod": {
        "open-mistral-7b": 32000,
        "Llama-3.1-70b": 128000,
        "Llama-3.1-405b": 128000,
        "Llama-3.3-70b": 128000,
        "Llama-3.1-8b": 128000,
        "gpt-4o": 128000,
        "gpt-4o-mini": 128000,
        "gpt-4-turbo": 128000,
        "claude-3-opus-latest": 200000,
        "gemini-1.5-flash-8b": 128000,
        "gemini-1.5-flash": 128000,
        "open-mixtral-8x7b": 32000,
        "open-mixtral-8x22b": 64000,
        "claude-3-5-sonnet-latest": 200000,
        "claude-3-haiku-20240307": 200000,
        "Qwen-2.5-Coder-32B": 32000,
        "Deepseek-R1-Distill-Llama-70B": 131072,
        "Deepseek-V3": 128000,
        "Qwen-2-VL-72B": 128000,
        "Deepseek-R1-Distill-Qwen-14B": 131072,
        "Deepseek-R1-Distill-Qwen-1.5B": 131072,
        "Deepseek-R1": 128000,
        "Deepseek-Llm-Chat-67B": 4096,
        "Qwen-2.5-7B": 132072,
        "Qwen-2.5-72B": 132072,
        "Qwen-2-72B": 128000,
        "o1": 200000,
        "gemini-2.0-flash-exp": 1000000,
        "grok-beta": 128000,
        "grok-2-latest": 128000,
    },
    "togetherai": {"Meta-Llama-3.1-70B-Instruct-Turbo": 128000},
}
